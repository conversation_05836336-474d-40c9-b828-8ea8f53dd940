@echo off
chcp 65001 >nul
echo ========================================
echo    SETUP DỰ ÁN WINFORMS - QUẢN LÝ ĐỀ TÀI
echo ========================================

echo.
echo [1/6] Kiểm tra .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: .NET SDK không được cài đặt!
    echo 📥 Vui lòng tải và cài đặt .NET 8.0 SDK từ:
    echo    https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
) else (
    echo ✅ .NET SDK đã được cài đặt
)

echo.
echo [2/6] Kiểm tra SQL Server...
sqlcmd -S .\sqlexpress -E -Q "SELECT 1" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  SQL Server Express không khả dụng, thử LocalDB...
    sqlcmd -S "(localdb)\MSSQLLocalDB" -E -Q "SELECT 1" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ ERROR: Không tìm thấy SQL Server!
        echo 📥 Vui lòng cài đặt SQL Server Express hoặc LocalDB
        pause
        exit /b 1
    ) else (
        echo ✅ LocalDB đã sẵn sàng
        set "SQL_SERVER=(localdb)\MSSQLLocalDB"
    )
) else (
    echo ✅ SQL Server Express đã sẵn sàng
    set "SQL_SERVER=.\sqlexpress"
)

echo.
echo [3/6] Restore dependencies...
dotnet restore WinFormsApp1.sln
if %errorlevel% neq 0 (
    echo ❌ ERROR: Không thể restore dependencies!
    pause
    exit /b 1
) else (
    echo ✅ Dependencies đã được restore
)

echo.
echo [4/6] Tạo database...
sqlcmd -S "%SQL_SERVER%" -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'Tung_DB') CREATE DATABASE Tung_DB" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Không thể tạo database, có thể đã tồn tại
) else (
    echo ✅ Database Tung_DB đã được tạo/kiểm tra
)

echo.
echo [5/6] Chạy migration...
cd Models
dotnet ef database update --startup-project ../WinFormsApp1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Migration thất bại!
    cd ..
    pause
    exit /b 1
) else (
    echo ✅ Migration hoàn thành
)
cd ..

echo.
echo [6/6] Build solution...
dotnet build WinFormsApp1.sln --configuration Debug
if %errorlevel% neq 0 (
    echo ❌ ERROR: Build thất bại!
    pause
    exit /b 1
) else (
    echo ✅ Build thành công
)

echo.
echo ========================================
echo    🎉 SETUP HOÀN THÀNH!
echo ========================================
echo.
echo 🚀 Để chạy ứng dụng, sử dụng một trong các lệnh sau:
echo.
echo    1. dotnet run --project WinFormsApp1/WinFormsApp1.csproj
echo    2. cd WinFormsApp1/bin/Debug/net8.0-windows ^&^& WinFormsApp1.exe
echo.
echo 📚 Xem thêm hướng dẫn chi tiết trong file: huongDanSetUp.md
echo.
pause
