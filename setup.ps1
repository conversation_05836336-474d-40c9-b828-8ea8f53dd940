# Setup Script cho Dự Án WinForms - Quản Lý Đề Tài
# PowerShell Version

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   SETUP DỰ ÁN WINFORMS - QUẢN LÝ ĐỀ TÀI" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function để kiểm tra command có tồn tại không
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Bước 1: Kiểm tra .NET SDK
Write-Host "[1/6] Kiểm tra .NET SDK..." -ForegroundColor Yellow
if (Test-Command "dotnet") {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK đã được cài đặt (Version: $dotnetVersion)" -ForegroundColor Green
} else {
    Write-Host "❌ ERROR: .NET SDK không được cài đặt!" -ForegroundColor Red
    Write-Host "📥 Vui lòng tải và cài đặt .NET 8.0 SDK từ:" -ForegroundColor Yellow
    Write-Host "   https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Blue
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

# Bước 2: Kiểm tra SQL Server
Write-Host ""
Write-Host "[2/6] Kiểm tra SQL Server..." -ForegroundColor Yellow
$sqlServer = $null

if (Test-Command "sqlcmd") {
    # Thử SQL Server Express trước
    try {
        $result = sqlcmd -S ".\sqlexpress" -E -Q "SELECT 1" -h -1 2>$null
        if ($LASTEXITCODE -eq 0) {
            $sqlServer = ".\sqlexpress"
            Write-Host "✅ SQL Server Express đã sẵn sàng" -ForegroundColor Green
        }
    } catch {
        # Thử LocalDB
        try {
            $result = sqlcmd -S "(localdb)\MSSQLLocalDB" -E -Q "SELECT 1" -h -1 2>$null
            if ($LASTEXITCODE -eq 0) {
                $sqlServer = "(localdb)\MSSQLLocalDB"
                Write-Host "✅ LocalDB đã sẵn sàng" -ForegroundColor Green
            }
        } catch {
            Write-Host "❌ ERROR: Không tìm thấy SQL Server!" -ForegroundColor Red
            Write-Host "📥 Vui lòng cài đặt SQL Server Express hoặc LocalDB" -ForegroundColor Yellow
            Read-Host "Nhấn Enter để thoát"
            exit 1
        }
    }
} else {
    Write-Host "❌ ERROR: sqlcmd không được tìm thấy!" -ForegroundColor Red
    Write-Host "📥 Vui lòng cài đặt SQL Server Command Line Utilities" -ForegroundColor Yellow
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

# Bước 3: Restore Dependencies
Write-Host ""
Write-Host "[3/6] Restore dependencies..." -ForegroundColor Yellow
try {
    dotnet restore WinFormsApp1.sln
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Dependencies đã được restore" -ForegroundColor Green
    } else {
        throw "Restore failed"
    }
} catch {
    Write-Host "❌ ERROR: Không thể restore dependencies!" -ForegroundColor Red
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

# Bước 4: Tạo Database
Write-Host ""
Write-Host "[4/6] Tạo database..." -ForegroundColor Yellow
try {
    sqlcmd -S $sqlServer -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'Tung_DB') CREATE DATABASE Tung_DB" 2>$null
    Write-Host "✅ Database Tung_DB đã được tạo/kiểm tra" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Không thể tạo database, có thể đã tồn tại" -ForegroundColor Yellow
}

# Bước 5: Chạy Migration
Write-Host ""
Write-Host "[5/6] Chạy migration..." -ForegroundColor Yellow
try {
    Set-Location "Models"
    dotnet ef database update --startup-project ../WinFormsApp1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migration hoàn thành" -ForegroundColor Green
    } else {
        throw "Migration failed"
    }
    Set-Location ".."
} catch {
    Write-Host "❌ ERROR: Migration thất bại!" -ForegroundColor Red
    Set-Location ".."
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

# Bước 6: Build Solution
Write-Host ""
Write-Host "[6/6] Build solution..." -ForegroundColor Yellow
try {
    dotnet build WinFormsApp1.sln --configuration Debug
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build thành công" -ForegroundColor Green
    } else {
        throw "Build failed"
    }
} catch {
    Write-Host "❌ ERROR: Build thất bại!" -ForegroundColor Red
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

# Hoàn thành
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🎉 SETUP HOÀN THÀNH!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 Để chạy ứng dụng, sử dụng một trong các lệnh sau:" -ForegroundColor Yellow
Write-Host ""
Write-Host "   1. dotnet run --project WinFormsApp1/WinFormsApp1.csproj" -ForegroundColor White
Write-Host "   2. cd WinFormsApp1/bin/Debug/net8.0-windows; ./WinFormsApp1.exe" -ForegroundColor White
Write-Host ""
Write-Host "📚 Xem thêm hướng dẫn chi tiết trong file: huongDanSetUp.md" -ForegroundColor Blue
Write-Host ""
Read-Host "Nhấn Enter để thoát"
